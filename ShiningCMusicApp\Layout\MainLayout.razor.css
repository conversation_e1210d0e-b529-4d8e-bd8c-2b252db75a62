.page {
    position: relative;
    display: flex;
    flex-direction: row;
    min-height: 100vh;
}

.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.top-row {
    background-color: #f7f7f7;
    border-bottom: 1px solid #d6d5d5;
    justify-content: flex-end;
    height: 3.5rem;
    display: flex;
    align-items: center;
}

    .top-row ::deep a, .top-row ::deep .btn-link {
        white-space: nowrap;
        margin-left: 1.5rem;
        text-decoration: none;
    }

    .top-row ::deep a:hover, .top-row ::deep .btn-link:hover {
        text-decoration: underline;
    }

    .top-row ::deep a:first-child {
        overflow: hidden;
        text-overflow: ellipsis;
    }

@media (max-width: 991.98px) {
    .page {
        flex-direction: column;
    }

    .main-content {
        margin-left: 0;
    }

    .top-row {
        position: sticky;
        top: 0;
        z-index: 1000;
        background: white;
        border-bottom: 1px solid #dee2e6;
        height: 3.5rem;
        padding-left: 1rem !important;
        padding-right: 1rem !important;
    }

    .content {
        padding-top: 1rem;
        padding-left: 1rem !important;
        padding-right: 1rem !important;
    }
}

@media (min-width: 992px) {
    .page {
        flex-direction: row;
    }

    .main-content {
        margin-left: 250px; /* Account for sidebar width */
    }

    .top-row {
        position: sticky;
        top: 0;
        z-index: 1;
        background: white;
        border-bottom: 1px solid #dee2e6;
    }

    .top-row, article {
        padding-left: 2rem !important;
        padding-right: 1.5rem !important;
    }
}
