@inherits LayoutComponentBase
@using ShiningCMusicApp.Services
@using Syncfusion.Blazor.Navigations
@using Syncfusion.Blazor.Buttons
@inject CustomAuthenticationStateProvider AuthStateProvider
@inject NavigationManager Navigation

<!-- MediaQuery component for responsive behavior -->
<SfMediaQuery @bind-ActiveBreakpoint="@activeBreakpoint"></SfMediaQuery>

<div class="page">
    <div id="wrapper">
        <!-- Top toolbar with menu toggle -->
        <div>
            <SfToolbar CssClass="top-toolbar">
                <ToolbarEvents Clicked="@Toggle"></ToolbarEvents>
                <ToolbarItems>
                    <ToolbarItem PrefixIcon="e-tbar-menu-icon tb-icons" TooltipText="Menu" CssClass="menu-toggle"></ToolbarItem>
                    <ToolbarItem>
                        <Template>
                            <div class="e-folder">
                                <div class="e-folder-name">
                                    <i class="fas fa-music me-2"></i>
                                    Shining C Music
                                </div>
                            </div>
                        </Template>
                    </ToolbarItem>
                    <ToolbarItem Align="ItemAlign.Right">
                        <Template>
                            <AuthorizeView>
                                <Authorized>
                                    <div class="d-flex align-items-center">
                                        <span class="text-dark me-3">Welcome, @context.User.Identity?.Name</span>
                                        <button class="btn btn-outline-primary btn-sm" @onclick="Logout">
                                            <i class="fas fa-sign-out-alt"></i> Logout
                                        </button>
                                    </div>
                                </Authorized>
                            </AuthorizeView>
                        </Template>
                    </ToolbarItem>
                </ToolbarItems>
            </SfToolbar>
        </div>

        <!-- Syncfusion Sidebar with default Blazor styling -->
        <SfSidebar @attributes="@HtmlAttribute"
                   Width="250px"
                   @bind-Type="@sidebarType"
                   Target=".e-main-content"
                   MediaQuery="(min-width:600px)"
                   @bind-IsOpen="SidebarToggle"
                   ShowBackdrop="@enableBackdrop"
                   OnOpen="Opened">
            <ChildContent>
                <NavMenu />
            </ChildContent>
        </SfSidebar>

        <!-- Main content area -->
        <div class="main-content e-main-content" id="main-text">
            <div class="sidebar-content">
                <div class="content px-4">
                    @Body
                </div>
            </div>
        </div>
    </div>
</div>

@code {
    private string activeBreakpoint = "Large";
    private bool enableBackdrop = false;
    public SidebarType sidebarType = SidebarType.Auto;
    public bool SidebarToggle = false;

    // Sidebar HTML attributes
    Dictionary<string, object> HtmlAttribute = new Dictionary<string, object>()
    {
        {"class", "sidebar-treeview" }
    };

    // MediaQuery responsive logic - key method from Syncfusion sample
    public void Opened()
    {
        if (activeBreakpoint == "Small")
        {
            sidebarType = SidebarType.Over;
            enableBackdrop = true;
        }
        else if (activeBreakpoint == "Medium")
        {
            sidebarType = SidebarType.Slide;
            enableBackdrop = false;
        }
        else
        {
            sidebarType = SidebarType.Push;
            enableBackdrop = false;
        }
    }

    // Toolbar toggle event
    public void Toggle(ClickEventArgs args)
    {
        if (args.Item.TooltipText == "Menu")
        {
            SidebarToggle = !SidebarToggle;
        }
    }

    protected override void OnInitialized()
    {
        base.OnInitialized();
        // Configure MediaQuery breakpoints like in Syncfusion sample
        SfMediaQuery.Small.MediaQuery = "(max-width: 600px)";
        SfMediaQuery.Medium.MediaQuery = "(min-width: 600px)";
        SfMediaQuery.Large.MediaQuery = "(min-width: 1100px)";
    }

    private async Task Logout()
    {
        await AuthStateProvider.LogoutAsync();
        Navigation.NavigateTo("/login");
    }

    private string GetShortName(string? fullName)
    {
        if (string.IsNullOrEmpty(fullName))
            return "";

        var parts = fullName.Split(' ');
        if (parts.Length > 1)
            return $"{parts[0][0]}.{parts[^1]}"; // First initial + last name

        return fullName.Length > 10 ? fullName[..10] + "..." : fullName;
    }
}
