@inherits LayoutComponentBase
@using ShiningCMusicApp.Services
@inject CustomAuthenticationStateProvider AuthStateProvider
@inject NavigationManager Navigation

<div class="page">
    <!-- Simple navigation without Syncfusion Sidebar for now -->
    <NavMenu />

    <!-- Main content area -->
    <div class="main-content">
        <!-- Top row with welcome and logout -->
        <div class="top-row px-4 d-flex justify-content-end align-items-center">
            <AuthorizeView>
                <Authorized>
                    <div class="d-flex align-items-center">
                        <span class="text-dark me-3">Welcome, @context.User.Identity?.Name</span>
                        <button class="btn btn-outline-primary btn-sm" @onclick="Logout">
                            <i class="fas fa-sign-out-alt"></i> Logout
                        </button>
                    </div>
                </Authorized>
            </AuthorizeView>
        </div>

        <!-- Page content -->
        <article class="content px-4">
            @Body
        </article>
    </div>
</div>

@code {
    private async Task Logout()
    {
        await AuthStateProvider.LogoutAsync();
        Navigation.NavigateTo("/login");
    }

    private string GetShortName(string? fullName)
    {
        if (string.IsNullOrEmpty(fullName))
            return "";

        var parts = fullName.Split(' ');
        if (parts.Length > 1)
            return $"{parts[0][0]}.{parts[^1]}"; // First initial + last name

        return fullName.Length > 10 ? fullName[..10] + "..." : fullName;
    }
}
