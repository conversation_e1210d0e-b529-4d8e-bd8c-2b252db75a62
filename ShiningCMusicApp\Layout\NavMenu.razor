@using ShiningCMusicApp.Services
@inject NavigationManager Navigation
@inject CustomAuthenticationStateProvider AuthStateProvider

<!-- MediaQuery component for responsive behavior -->
<SfMediaQuery @bind-ActiveBreakpoint="@activeBreakpoint"></SfMediaQuery>

<SfSidebar @ref="sidebarObj"
           @bind-IsOpen="@isOpen"
           Type="@sidebarType"
           Position="SidebarPosition.Left"
           Width="250px"
           ShowBackdrop="@enableBackdrop"
           CloseOnDocumentClick="@closeOnDocumentClick"
           CssClass="custom-sidebar"
           OnOpen="OnSidebarOpen">

    <ChildContent>
        <!-- Sidebar Header -->
        <div class="sidebar-header">
            <div class="d-flex justify-content-between align-items-center p-3">
                <a class="navbar-brand text-white" href="">
                    <i class="fas fa-music me-2"></i>
                    <span>Shining C Music</span>
                </a>
                <!-- Toggle button for mobile -->
                <button class="btn btn-link text-white d-lg-none" @onclick="ToggleSidebar">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>

        <!-- Sidebar Content -->
        <div class="sidebar-content">
            <nav class="flex-column">
                <AuthorizeView Roles="Administrator">
                    <Authorized>
                        <div class="nav-item px-3">
                            <NavLink class="nav-link" href="" Match="NavLinkMatch.All" @onclick="OnNavLinkClick">
                                <span class="bi bi-house-door-fill-nav-menu" aria-hidden="true"></span> Home
                            </NavLink>
                        </div>
                        <div class="nav-item px-3">
                            <NavLink class="nav-link" href="lessons" @onclick="OnNavLinkClick">
                                <span class="bi bi-calendar-event-nav-menu" aria-hidden="true"></span> Lessons
                            </NavLink>
                        </div>
                        <div class="nav-item px-3">
                            <NavLink class="nav-link" href="tutors" @onclick="OnNavLinkClick">
                                <span class="bi bi-person-fill-nav-menu" aria-hidden="true"></span> Tutors
                            </NavLink>
                        </div>
                        <div class="nav-item px-3">
                            <NavLink class="nav-link" href="students" @onclick="OnNavLinkClick">
                                <span class="bi bi-people-fill-nav-menu" aria-hidden="true"></span> Students
                            </NavLink>
                        </div>
                        <div class="nav-item px-3">
                            <NavLink class="nav-link" href="admin" @onclick="OnNavLinkClick">
                                <span class="bi bi-gear-fill-nav-menu" aria-hidden="true"></span> Admin
                            </NavLink>
                        </div>
                    </Authorized>
                </AuthorizeView>
                <AuthorizeView Roles="Tutor,Student">
                    <Authorized>
                        <div class="nav-item px-3">
                            <NavLink class="nav-link" href="lessons" @onclick="OnNavLinkClick">
                                <span class="bi bi-calendar-event-nav-menu" aria-hidden="true"></span> My Lessons
                            </NavLink>
                        </div>
                    </Authorized>
                </AuthorizeView>

@*                 <div class="nav-item px-3">
                    <NavLink class="nav-link" href="scheduler" @onclick="OnNavLinkClick">
                        <span class="bi bi-plus-square-fill-nav-menu" aria-hidden="true"></span> Scheduler
                    </NavLink>
                </div>
                <div class="nav-item px-3">
                    <NavLink class="nav-link" href="weather" @onclick="OnNavLinkClick">
                        <span class="bi bi-list-nested-nav-menu" aria-hidden="true"></span> Weather
                    </NavLink>
                </div> *@
            </nav>
        </div>
    </ChildContent>
</SfSidebar>

<!-- Toggle button for mobile (outside sidebar) -->
<button class="mobile-sidebar-toggle d-lg-none" @onclick="ToggleSidebar" style="position: fixed; top: 10px; left: 10px; z-index: 1001; background: #1b6ec2; border: none; color: white; padding: 8px 12px; border-radius: 4px;">
    <i class="fas fa-bars"></i>
</button>

@code {
    private SfSidebar? sidebarObj;
    private string activeBreakpoint = "Large"; // Default to Large breakpoint
    private bool isOpen = true; // Default open for desktop
    private SidebarType sidebarType = SidebarType.Auto; // Default for desktop
    private bool enableBackdrop = false; // Default for desktop
    private bool closeOnDocumentClick = false; // Default for desktop

    protected override void OnInitialized()
    {
        base.OnInitialized();

        // Configure MediaQuery breakpoints
        SfMediaQuery.Small.MediaQuery = "(max-width: 991.98px)"; // Mobile/tablet
        SfMediaQuery.Medium.MediaQuery = "(min-width: 992px) and (max-width: 1199.98px)"; // Small desktop
        SfMediaQuery.Large.MediaQuery = "(min-width: 1200px)"; // Large desktop

        // Set initial configuration based on default breakpoint
        ConfigureSidebarForBreakpoint();
    }

    protected override void OnParametersSet()
    {
        base.OnParametersSet();
        ConfigureSidebarForBreakpoint();
    }

    private void ConfigureSidebarForBreakpoint()
    {
        if (activeBreakpoint == "Small")
        {
            // Mobile/tablet configuration
            isOpen = false; // Closed by default on mobile
            sidebarType = SidebarType.Over; // Overlay type
            enableBackdrop = true; // Enable backdrop/dimming
            closeOnDocumentClick = true; // Close when clicking outside
        }
        else
        {
            // Desktop configuration (Medium and Large)
            isOpen = true; // Open by default on desktop
            sidebarType = SidebarType.Auto; // Push content
            enableBackdrop = false; // No backdrop needed
            closeOnDocumentClick = false; // Don't close on document click
        }
    }

    private void OnSidebarOpen()
    {
        // This event is triggered when sidebar opens
        // Update the configuration based on current breakpoint
        ConfigureSidebarForBreakpoint();
        StateHasChanged();
    }

    private void ToggleSidebar()
    {
        // Toggle the IsOpen property to show/hide sidebar
        isOpen = !isOpen;
    }

    private void OnNavLinkClick()
    {
        // Close sidebar on mobile when navigation link is clicked
        if (activeBreakpoint == "Small" && isOpen)
        {
            isOpen = false;
        }
    }

    private async Task Logout()
    {
        await AuthStateProvider.LogoutAsync();
        Navigation.NavigateTo("/login");
    }
}
